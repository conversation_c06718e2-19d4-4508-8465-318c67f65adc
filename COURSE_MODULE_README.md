# Course Management Module

This module provides a comprehensive course management system for the admin dashboard with the following features:

## Features Implemented

### 1. Course Management
- **Course Name**: Arabic and English names (minimum 5 characters each)
- **Course Image**: Upload using Spatie Media Library
- **Instructor Name**: Arabic and English names (minimum 5 characters each)
- **Course Duration**: In hours (minimum 1 hour)
- **Course Description**: Arabic and English descriptions (minimum 20 characters each)
- **Course Price**: Numeric price field
- **Course Visibility**: Toggle for customer visibility
- **Search Functionality**: Search by name, instructor, price range, and status

### 2. Course Stages Management
- **Stage Title**: Arabic and English titles (minimum 5 characters each)
- **Video Upload**: Video files for each stage (max 50MB)
- **Add/Edit/Delete Stages**: Dynamic stage management
- **Minimum Requirement**: At least one stage per course
- **Stage Ordering**: Automatic ordering of stages

### 3. Course Listing
- **Paginated Display**: Shows all courses with pagination
- **Course Details**: Name, instructor, duration, price, stage count
- **Status Management**: Enable/disable courses for customers
- **Actions**: Edit, view, delete functionality
- **Bulk Operations**: Delete multiple courses

## Files Created

### Database Migrations
- `database/migrations/2025_01_20_120000_create_courses_table.php`
- `database/migrations/2025_01_20_120001_create_course_stages_table.php`

### Models
- `app/Models/Course.php` - Main course model with Spatie Media Library integration
- `app/Models/CourseStage.php` - Course stages model with video upload support

### Controllers
- `app/Http/Controllers/Admin/CourseController.php` - Complete CRUD operations

### Form Requests
- `app/Http/Requests/Admin/Course/Store.php` - Validation for course creation
- `app/Http/Requests/Admin/Course/Update.php` - Validation for course updates

### Views
- `resources/views/admin/courses/index.blade.php` - Course listing page
- `resources/views/admin/courses/create.blade.php` - Course creation form
- `resources/views/admin/courses/edit.blade.php` - Course editing form
- `resources/views/admin/courses/show.blade.php` - Course details view
- `resources/views/admin/courses/table.blade.php` - Course table component

### Routes
- Added complete course management routes in `routes/web.php`

## Installation Steps

1. **Run Migrations**:
   ```bash
   php artisan migrate
   ```

2. **Create Storage Link** (if not already created):
   ```bash
   php artisan storage:link
   ```

3. **Set Permissions** for media uploads:
   ```bash
   chmod -R 755 storage/
   chmod -R 755 public/storage/
   ```

## Usage

### Accessing the Course Module
- Navigate to `/admin/courses` in your admin panel
- The module will appear in the dashboard navigation

### Creating a Course
1. Click "إضافة دورة" (Add Course) button
2. Fill in all required fields:
   - Course name in Arabic and English
   - Instructor name in Arabic and English
   - Duration in hours
   - Description in both languages
   - Price
   - Course image
3. Add at least one stage with title and video
4. Set visibility status
5. Click "حفظ" (Save)

### Managing Course Stages
- Use the "إضافة مرحلة" (Add Stage) button to add new stages
- Each stage requires:
  - Title in Arabic and English (min 5 characters)
  - Video file (max 50MB)
- Use "حذف المرحلة" (Delete Stage) to remove stages
- At least one stage is required per course

### Course Operations
- **View**: Click the eye icon to view course details
- **Edit**: Click the edit icon to modify course information
- **Delete**: Click the trash icon to delete a course
- **Toggle Status**: Click the status badge to enable/disable course visibility
- **Bulk Delete**: Select multiple courses and use the delete all button

## Technical Details

### Media Collections
- **Courses**: `courses` collection for course images
- **Stage Videos**: `stage-videos` collection for stage video files

### Validation Rules
- Course names: minimum 5 characters
- Instructor names: minimum 5 characters
- Descriptions: minimum 20 characters
- Duration: minimum 1 hour
- Images: JPEG, PNG, JPG, GIF (max 2MB)
- Videos: MP4, AVI, MOV, WMV (max 50MB)

### Search Functionality
- Search by course name
- Search by instructor name
- Filter by price range
- Filter by active/inactive status

## Dependencies
- Spatie Media Library (already configured)
- Laravel Translatable (for multilingual support)
- SweetAlert2 (for user notifications)

## Notes
- All text fields support Arabic and English languages
- File uploads are handled securely through Spatie Media Library
- The module follows the existing dashboard structure and patterns
- Responsive design compatible with the existing admin theme
