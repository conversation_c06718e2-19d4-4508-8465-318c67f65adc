<?php

namespace Database\Seeders;

use App\Models\Course;
use App\Models\CourseStage;
use Illuminate\Database\Seeder;

class CourseSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create a sample course
        $course = Course::create([
            'name' => [
                'ar' => 'دورة تطوير المواقع الإلكترونية',
                'en' => 'Web Development Course'
            ],
            'instructor_name' => [
                'ar' => 'أحمد محمد علي',
                'en' => '<PERSON>'
            ],
            'duration' => 40.5,
            'description' => [
                'ar' => 'دورة شاملة في تطوير المواقع الإلكترونية باستخدام أحدث التقنيات والأدوات المتاحة في السوق',
                'en' => 'Comprehensive course in web development using the latest technologies and tools available in the market'
            ],
            'price' => 299.99,
            'is_active' => true
        ]);

        // Create sample stages for the course
        CourseStage::create([
            'course_id' => $course->id,
            'title' => [
                'ar' => 'مقدمة في تطوير المواقع',
                'en' => 'Introduction to Web Development'
            ],
            'order' => 1
        ]);

        CourseStage::create([
            'course_id' => $course->id,
            'title' => [
                'ar' => 'أساسيات HTML و CSS',
                'en' => 'HTML and CSS Fundamentals'
            ],
            'order' => 2
        ]);

        CourseStage::create([
            'course_id' => $course->id,
            'title' => [
                'ar' => 'البرمجة بـ JavaScript',
                'en' => 'JavaScript Programming'
            ],
            'order' => 3
        ]);

        // Create another sample course
        $course2 = Course::create([
            'name' => [
                'ar' => 'دورة تصميم الجرافيك',
                'en' => 'Graphic Design Course'
            ],
            'instructor_name' => [
                'ar' => 'فاطمة أحمد حسن',
                'en' => 'Fatima Ahmed Hassan'
            ],
            'duration' => 25.0,
            'description' => [
                'ar' => 'تعلم أساسيات تصميم الجرافيك والعمل مع برامج التصميم المختلفة',
                'en' => 'Learn the fundamentals of graphic design and work with various design software'
            ],
            'price' => 199.99,
            'is_active' => false
        ]);

        // Create sample stages for the second course
        CourseStage::create([
            'course_id' => $course2->id,
            'title' => [
                'ar' => 'مقدمة في التصميم',
                'en' => 'Introduction to Design'
            ],
            'order' => 1
        ]);

        CourseStage::create([
            'course_id' => $course2->id,
            'title' => [
                'ar' => 'استخدام الفوتوشوب',
                'en' => 'Using Photoshop'
            ],
            'order' => 2
        ]);
    }
}
