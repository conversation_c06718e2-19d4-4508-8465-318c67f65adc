<?php

namespace App\Http\Controllers\Admin;

use App\Models\Course;
use App\Traits\Report;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Course\Store;
use App\Http\Requests\Admin\Course\Update;

class CourseController extends Controller
{
    use Report;
    public function index($id = null)
    {
        if (request()->ajax()) {
            $searchArray = request()->searchArray ?? [];

            // Debug: Check if courses exist
            $totalCourses = Course::count();
            \Log::info('Total courses in database: ' . $totalCourses);
            \Log::info('Search array: ', $searchArray);

            $courses = Course::with('stages')->search($searchArray)->paginate(30);
            \Log::info('Courses returned by query: ' . $courses->count());

            $html = view('admin.courses.table', compact('courses'))->render();
            return response()->json(['html' => $html]);
        }
        return view('admin.courses.index');
    }

    public function create()
    {
        return view('admin.courses.create');
    }

    public function store(Store $request)
    {
        try {
            // Create the course
            $course = Course::create($request->validated());

            // Handle course image upload
            if ($request->hasFile('image')) {
                $course->addMediaFromRequest('image')->toMediaCollection('courses');
            }

            // Handle course stages
            if ($request->has('stages')) {
                foreach ($request->stages as $index => $stageData) {
                    $stage = $course->stages()->create([
                        'title' => [
                            'ar' => $stageData['title']['ar'],
                            'en' => $stageData['title']['en']
                        ],
                        'order' => $index + 1
                    ]);

                    // Handle stage video upload
                    if (isset($stageData['video']) && $stageData['video']) {
                        $stage->addMedia($stageData['video'])->toMediaCollection('stage-videos');
                    }
                }
            }

            Report::addToLog('إضافة دورة تدريبية');
            return response()->json(['url' => route('admin.courses.index')]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء إضافة الدورة'], 500);
        }
    }

    public function edit($id)
    {
        $course = Course::with('stages')->findOrFail($id);
        return view('admin.courses.edit', compact('course'));
    }

    public function update(Update $request, $id)
    {
        try {
            $course = Course::findOrFail($id);
            $course->update($request->validated());

            // Handle course image upload
            if ($request->hasFile('image')) {
                $course->clearMediaCollection('courses');
                $course->addMediaFromRequest('image')->toMediaCollection('courses');
            }

            // Handle course stages update
            if ($request->has('stages')) {
                // Delete existing stages
                $course->stages()->delete();

                // Create new stages
                foreach ($request->stages as $index => $stageData) {
                    $stage = $course->stages()->create([
                        'title' => [
                            'ar' => $stageData['title']['ar'],
                            'en' => $stageData['title']['en']
                        ],
                        'order' => $index + 1
                    ]);

                    // Handle stage video upload
                    if (isset($stageData['video']) && $stageData['video']) {
                        $stage->addMedia($stageData['video'])->toMediaCollection('stage-videos');
                    }
                    // Note: Video removal is handled by deleting and recreating stages
                    // If remove_video is set to 1, we simply don't add the video back
                }
            }

            Report::addToLog('تعديل دورة تدريبية');
            return response()->json(['url' => route('admin.courses.index')]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء تعديل الدورة'], 500);
        }
    }

    public function show($id)
    {
        $course = Course::with('stages')->findOrFail($id);
        return view('admin.courses.show', compact('course'));
    }

    public function destroy($id)
    {
        try {
            $course = Course::findOrFail($id);
            $course->delete();
            Report::addToLog('حذف دورة تدريبية');
            return response()->json(['id' => $id]);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء حذف الدورة'], 500);
        }
    }

    public function destroyAll(Request $request)
    {
        try {
            $requestIds = json_decode($request->data);

            foreach ($requestIds as $id) {
                $ids[] = $id->id;
            }

            if (Course::whereIn('id', $ids)->delete()) {
                Report::addToLog('حذف متعدد للدورات التدريبية');
                return response()->json('success');
            } else {
                return response()->json('failed');
            }
        } catch (\Exception $e) {
            return response()->json('failed');
        }
    }

    public function toggleStatus(Request $request)
    {
        try {
            $course = Course::findOrFail($request->id);
            $course->update(['is_active' => !$course->is_active]);
            Report::addToLog('تغيير حالة الدورة التدريبية');
            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ أثناء تغيير حالة الدورة'], 500);
        }
    }
}
