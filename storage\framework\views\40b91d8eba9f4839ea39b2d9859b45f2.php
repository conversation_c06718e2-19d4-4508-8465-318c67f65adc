<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/index_page.css')); ?>">
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<?php if (isset($component)) { $__componentOriginal781089cd478f3e09d520a65f160df974 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal781089cd478f3e09d520a65f160df974 = $attributes; } ?>
<?php $component = App\View\Components\Admin\Table::resolve(['datefilter' => 'true','order' => 'true','addbutton' => ''.e(route('admin.courses.create')).'','deletebutton' => ''.e(route('admin.courses.deleteAll')).'','searchArray' => [
        'name' => [
            'input_type' => 'text' ,
            'input_name' => 'اسم الدورة' ,
        ] ,
        'instructor_name' => [
            'input_type' => 'text' ,
            'input_name' => 'اسم المدرب' ,
        ] ,
        'price_from' => [
            'input_type' => 'number' ,
            'input_name' => 'السعر من' ,
        ] ,
        'price_to' => [
            'input_type' => 'number' ,
            'input_name' => 'السعر إلى' ,
        ] ,
        'is_active' => [
            'input_type' => 'select' ,
            'rows' => [
                ['id' => 1, 'name' => 'مفعل'],
                ['id' => 0, 'name' => 'غير مفعل']
            ] ,
            'input_name' => 'الحالة' ,
        ] ,
    ]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('admin.table'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\Admin\Table::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('tableContent', null, []); ?> 
        <div class="table_content_append card">

        </div>
     <?php $__env->endSlot(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $attributes = $__attributesOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__attributesOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal781089cd478f3e09d520a65f160df974)): ?>
<?php $component = $__componentOriginal781089cd478f3e09d520a65f160df974; ?>
<?php unset($__componentOriginal781089cd478f3e09d520a65f160df974); ?>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
    <script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
    <script src="<?php echo e(asset('admin/index_page.js')); ?>"></script>
    <?php echo $__env->make('admin.shared.deleteOne', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('admin.shared.deleteAll', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
    <?php echo $__env->make('admin.shared.filter_js', ['index_route' => url('admin/courses')], array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <script>
        // Setup CSRF token for AJAX requests
        $.ajaxSetup({
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            }
        });

        $(document).on('click', '.toggle-status', function() {
            let id = $(this).data('id');
            let button = $(this);

            $.ajax({
                url: '<?php echo e(route("admin.courses.toggleStatus")); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    id: id
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Reload the table using the shared getData function
                        getData({'searchArray' : searchArray()});
                    }
                },
                error: function() {
                    Swal.fire({
                        title: 'خطأ!',
                        text: 'حدث خطأ أثناء تغيير حالة الدورة',
                        type: 'error'
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/courses/index.blade.php ENDPATH**/ ?>