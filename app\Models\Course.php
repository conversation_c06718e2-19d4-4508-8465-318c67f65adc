<?php

namespace App\Models;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Course extends Model implements HasMedia
{
    use HasFactory, HasTranslations, InteractsWithMedia;

    protected $fillable = [
        'name',
        'instructor_name', 
        'duration',
        'description',
        'price',
        'is_active'
    ];

    public $translatable = ['name', 'instructor_name', 'description'];

    protected $casts = [
        'duration' => 'decimal:2',
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('courses')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/courses/default.png'))
            ->useFallbackPath(public_path('storage/images/courses/default.png'));
    }

    /**
     * Get the stages for the course
     */
    public function stages()
    {
        return $this->hasMany(CourseStage::class)->orderBy('order');
    }

    /**
     * Get the stages count attribute
     */
    public function getStagesCountAttribute()
    {
        return $this->stages()->count();
    }

    /**
     * Get the course image
     */
    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('courses') ?: asset('storage/images/courses/default.png');
    }

    /**
     * Scope for search functionality
     */
    public function scopeSearch($query, $searchArray)
    {
        if (isset($searchArray['name']) && !empty($searchArray['name'])) {
            $query->where('name->ar', 'like', '%' . $searchArray['name'] . '%')
                  ->orWhere('name->en', 'like', '%' . $searchArray['name'] . '%');
        }

        if (isset($searchArray['instructor_name']) && !empty($searchArray['instructor_name'])) {
            $query->where('instructor_name->ar', 'like', '%' . $searchArray['instructor_name'] . '%')
                  ->orWhere('instructor_name->en', 'like', '%' . $searchArray['instructor_name'] . '%');
        }

        if (isset($searchArray['price_from']) && !empty($searchArray['price_from'])) {
            $query->where('price', '>=', $searchArray['price_from']);
        }

        if (isset($searchArray['price_to']) && !empty($searchArray['price_to'])) {
            $query->where('price', '<=', $searchArray['price_to']);
        }

        if (isset($searchArray['is_active']) && $searchArray['is_active'] !== '') {
            $query->where('is_active', $searchArray['is_active']);
        }

        return $query;
    }
}
