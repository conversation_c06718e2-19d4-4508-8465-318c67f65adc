<?php

namespace App\Models;

use Spatie\MediaLibrary\HasMedia;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Course extends Model implements HasMedia
{
    use HasFactory, HasTranslations, InteractsWithMedia;

    protected $fillable = [
        'name',
        'instructor_name',
        'duration',
        'description',
        'price',
        'is_active'
    ];

    public $translatable = ['name', 'instructor_name', 'description'];

    protected $casts = [
        'duration' => 'decimal:2',
        'price' => 'decimal:2',
        'is_active' => 'boolean'
    ];

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('courses')
            ->singleFile()
            ->useFallbackUrl(asset('storage/images/courses/default.png'))
            ->useFallbackPath(public_path('storage/images/courses/default.png'));
    }

    /**
     * Get the stages for the course
     */
    public function stages()
    {
        return $this->hasMany(CourseStage::class)->orderBy('order');
    }

    /**
     * Get the stages count attribute
     */
    public function getStagesCountAttribute()
    {
        return $this->stages()->count();
    }

    /**
     * Get the course image
     */
    public function getImageAttribute()
    {
        return $this->getFirstMediaUrl('courses') ?: asset('storage/images/courses/default.png');
    }

    /**
     * Scope for search functionality
     */
    public function scopeSearch($query, $searchArray = [])
    {
        $query->where(function ($query) use ($searchArray) {
            if ($searchArray) {
                foreach ($searchArray as $key => $value) {
                    if ($key == 'name' && !empty($value)) {
                        $query->where(function($q) use ($value) {
                            $q->where('name->ar', 'like', '%' . $value . '%')
                              ->orWhere('name->en', 'like', '%' . $value . '%');
                        });
                    } elseif ($key == 'instructor_name' && !empty($value)) {
                        $query->where(function($q) use ($value) {
                            $q->where('instructor_name->ar', 'like', '%' . $value . '%')
                              ->orWhere('instructor_name->en', 'like', '%' . $value . '%');
                        });
                    } elseif ($key == 'price_from' && !empty($value)) {
                        $query->where('price', '>=', $value);
                    } elseif ($key == 'price_to' && !empty($value)) {
                        $query->where('price', '<=', $value);
                    } elseif ($key == 'is_active' && $value !== '') {
                        $query->where('is_active', $value);
                    } elseif ($key == 'created_at_min' && !empty($value)) {
                        $query->whereDate('created_at', '>=', $value);
                    } elseif ($key == 'created_at_max' && !empty($value)) {
                        $query->whereDate('created_at', '<=', $value);
                    } elseif ($key == 'order') {
                        // Skip order parameter
                    } elseif (!empty($value)) {
                        $query->where($key, 'like', '%' . $value . '%');
                    }
                }
            }
        });

        return $query->orderBy('created_at', request()->searchArray && request()->searchArray['order'] ? request()->searchArray['order'] : 'DESC');
    }
}
