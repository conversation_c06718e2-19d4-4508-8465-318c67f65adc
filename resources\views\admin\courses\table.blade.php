<div class="position-relative" style="overflow: auto">
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>التاريخ</th>
                <th>الصورة</th>
                <th>اسم الدورة</th>
                <th>المدرب</th>
                <th>المدة (ساعة)</th>
                <th>السعر</th>
                <th>عدد المراحل</th>
                <th>الحالة</th>
                <th>التحكم</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($courses as $course)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{ $course->id }}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $course->created_at->format('d/m/Y') }}</td>
                    <td>
                        <img src="{{ $course->image }}" width="50px" height="50px" alt="course image" style="border-radius: 5px;">
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> {{ $course->getTranslation('name', 'ar') }}<br>
                            <strong>English:</strong> {{ $course->getTranslation('name', 'en') }}
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> {{ $course->getTranslation('instructor_name', 'ar') }}<br>
                            <strong>English:</strong> {{ $course->getTranslation('instructor_name', 'en') }}
                        </div>
                    </td>
                    <td>{{ $course->duration }}</td>
                    <td>{{ number_format($course->price, 2) }} ريال</td>
                    <td>
                        <span class="badge badge-info">{{ $course->stages_count }} مرحلة</span>
                    </td>
                    <td>
                        @if ($course->is_active)
                            <span class="btn btn-sm round btn-outline-success toggle-status" data-id="{{ $course->id }}">
                                مفعل <i class="la la-check font-medium-2"></i>
                            </span>
                        @else
                            <span class="btn btn-sm round btn-outline-danger toggle-status" data-id="{{ $course->id }}">
                                غير مفعل <i class="la la-close font-medium-2"></i>
                            </span>
                        @endif
                    </td>
                    <td class="text-center">
                        <div class="btn-group" role="group" aria-label="Second group">
                            <a href="{{ route('admin.courses.edit', $course->id) }}" class="btn btn-sm btn-primary">
                                <i class="la la-edit"></i>
                            </a>
                            <a href="{{ route('admin.courses.show', $course->id) }}" class="btn btn-sm btn-info">
                                <i class="la la-eye"></i>
                            </a>
                            <a href="#" class="btn btn-sm btn-danger delete" data-id="{{ $course->id }}">
                                <i class="la la-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>
    
    <div class="d-flex justify-content-center">
        {{ $courses->appends(request()->query())->links() }}
    </div>
</div>
