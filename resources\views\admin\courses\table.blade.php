<div class="position-relative" style="overflow: auto">
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>التاريخ</th>
                <th>الصورة</th>
                <th>اسم الدورة</th>
                <th>المدرب</th>
                <th>المدة (ساعة)</th>
                <th>السعر</th>
                <th>عدد المراحل</th>
                <th>الحالة</th>
                <th>التحكم</th>
            </tr>
        </thead>
        <tbody>
            @forelse ($courses as $course)
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="{{ $course->id }}">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td>{{ $course->created_at->format('d/m/Y') }}</td>
                    <td>
                        <img src="{{ $course->image }}" width="50px" height="50px" alt="course image" style="border-radius: 5px;">
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> {{ $course->getTranslation('name', 'ar') }}<br>
                            <strong>English:</strong> {{ $course->getTranslation('name', 'en') }}
                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> {{ $course->getTranslation('instructor_name', 'ar') }}<br>
                            <strong>English:</strong> {{ $course->getTranslation('instructor_name', 'en') }}
                        </div>
                    </td>
                    <td>{{ $course->duration }}</td>
                    <td>{{ number_format($course->price, 2) }} ريال</td>
                    <td>
                        <span class="badge badge-info">{{ $course->stages_count }} مرحلة</span>
                    </td>
                    <td>
                        @if ($course->is_active)
                            <span class="btn btn-sm round btn-outline-success toggle-status" data-id="{{ $course->id }}">
                                مفعل <i class="la la-check font-medium-2"></i>
                            </span>
                        @else
                            <span class="btn btn-sm round btn-outline-danger toggle-status" data-id="{{ $course->id }}">
                                غير مفعل <i class="la la-close font-medium-2"></i>
                            </span>
                        @endif
                    </td>
                    <td class="product-action">
                        <span class="text-primary"><a href="{{ route('admin.courses.show', ['id' => $course->id]) }}" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> {{ __('admin.show') }}</a></span>
                        <span class="action-edit text-primary"><a href="{{ route('admin.courses.edit', ['id' => $course->id]) }}" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i>{{ __('admin.edit') }}</a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="{{ url('admin/courses/' . $course->id) }}"><i class="feather icon-trash"></i>{{ __('admin.delete') }}</span>
                    </td>
                </tr>
            @empty
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="alert alert-info">
                            <i class="la la-info-circle"></i> لا توجد دورات تدريبية حتى الآن
                        </div>
                    </td>
                </tr>
            @endforelse
        </tbody>
    </table>

    @if($courses->hasPages())
        <div class="d-flex justify-content-center">
            {{ $courses->appends(request()->query())->links() }}
        </div>
    @endif
</div>
