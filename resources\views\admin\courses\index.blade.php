@extends('admin.layout.master')

@section('css')
    <link rel="stylesheet" type="text/css" href="{{asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')}}">
    <link rel="stylesheet" type="text/css" href="{{asset('admin/index_page.css')}}">
@endsection

@section('content')

<x-admin.table
    datefilter="true"
    order="true"
    addbutton="{{ route('admin.courses.create') }}"
    deletebutton="{{ route('admin.courses.deleteAll') }}"
    :searchArray="[
        'name' => [
            'input_type' => 'text' ,
            'input_name' => 'اسم الدورة' ,
        ] ,
        'instructor_name' => [
            'input_type' => 'text' ,
            'input_name' => 'اسم المدرب' ,
        ] ,
        'price_from' => [
            'input_type' => 'number' ,
            'input_name' => 'السعر من' ,
        ] ,
        'price_to' => [
            'input_type' => 'number' ,
            'input_name' => 'السعر إلى' ,
        ] ,
        'is_active' => [
            'input_type' => 'select' ,
            'rows' => [
                ['id' => 1, 'name' => 'مفعل'],
                ['id' => 0, 'name' => 'غير مفعل']
            ] ,
            'input_name' => 'الحالة' ,
        ] ,
    ]"
>
    <x-slot name="tableContent">
        <div class="table_content_append card">

        </div>
    </x-slot>
</x-admin.table>

@endsection

@section('js')
    <script src="{{asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')}}"></script>
    <script src="{{asset('admin/index_page.js')}}"></script>

    <script>
        // Load table data on page load
        $(document).ready(function() {
            loadTable();
        });

        function loadTable() {
            $.ajax({
                type: "get",
                url: "{{ route('admin.courses.index') }}",
                data: {'searchArray': {}},
                dataType: "json",
                cache: false,
                success: function (response) {
                    if (response.html) {
                        $('.table_content_append').html(response.html);
                    } else {
                        console.error('No HTML in response:', response);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('Error loading table data:', {
                        status: status,
                        error: error,
                        response: xhr.responseText
                    });
                    $('.table_content_append').html('<div class="alert alert-danger">خطأ في تحميل البيانات</div>');
                }
            });
        }

        $(document).on('click', '.toggle-status', function() {
            let id = $(this).data('id');
            let button = $(this);

            $.ajax({
                url: '{{ route("admin.courses.toggleStatus") }}',
                type: 'POST',
                data: {
                    _token: '{{ csrf_token() }}',
                    id: id
                },
                success: function(response) {
                    if (response.status === 'success') {
                        // Reload the table
                        loadTable();
                    }
                },
                error: function() {
                    Swal.fire('خطأ!', 'حدث خطأ أثناء تغيير حالة الدورة', 'error');
                }
            });
        });
    </script>
@endsection
