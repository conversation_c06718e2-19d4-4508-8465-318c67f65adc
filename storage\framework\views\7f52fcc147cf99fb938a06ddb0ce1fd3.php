<?php $__env->startSection('css'); ?>
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/css-rtl/plugins/forms/validation/form-validation.css')); ?>">
    <link rel="stylesheet" type="text/css" href="<?php echo e(asset('admin/app-assets/vendors/css/extensions/sweetalert2.min.css')); ?>">
    <style>
        .stage-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 15px;
            background-color: #f9f9f9;
        }
        .stage-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }
        .remove-stage {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 3px;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Basic multiple Column Form section start -->
<form method="POST" action="<?php echo e(route('admin.courses.store')); ?>" class="store form-horizontal" novalidate enctype="multipart/form-data">
<section id="multiple-column-form">
    <div class="row">
        <div class="col-md-3">
            <div class="col-12 card card-body">
                <div class="imgMontg col-12 text-center">
                    <div class="dropBox">
                        <div class="textCenter">
                            <div class="imagesUploadBlock">
                                <label class="uploadImg">
                                    <span><i class="feather icon-image"></i></span>
                                    <input type="file" accept="image/*" name="image" class="imageUploader" required>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-9">
            <div class="card">
                <div class="card-content">
                    <div class="card-body">
                        <?php echo csrf_field(); ?>
                        <div class="form-body">
                            <div class="row">
                                <!-- Course Name Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="name_ar">اسم الدورة (عربي) <span class="text-danger">*</span></label>
                                        <input type="text" id="name_ar" class="form-control" name="name[ar]" required minlength="5">
                                    </div>
                                </div>

                                <!-- Course Name English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="name_en">اسم الدورة (إنجليزي) <span class="text-danger">*</span></label>
                                        <input type="text" id="name_en" class="form-control" name="name[en]" required minlength="5">
                                    </div>
                                </div>

                                <!-- Instructor Name Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="instructor_name_ar">اسم المدرب (عربي) <span class="text-danger">*</span></label>
                                        <input type="text" id="instructor_name_ar" class="form-control" name="instructor_name[ar]" required minlength="5">
                                    </div>
                                </div>

                                <!-- Instructor Name English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="instructor_name_en">اسم المدرب (إنجليزي) <span class="text-danger">*</span></label>
                                        <input type="text" id="instructor_name_en" class="form-control" name="instructor_name[en]" required minlength="5">
                                    </div>
                                </div>

                                <!-- Duration -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="duration">مدة الدورة (بالساعات) <span class="text-danger">*</span></label>
                                        <input type="number" id="duration" class="form-control" name="duration" required min="1" step="0.5">
                                    </div>
                                </div>

                                <!-- Price -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="price">سعر الدورة <span class="text-danger">*</span></label>
                                        <input type="number" id="price" class="form-control" name="price" required min="0" step="0.01">
                                    </div>
                                </div>

                                <!-- Description Arabic -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="description_ar">وصف الدورة (عربي) <span class="text-danger">*</span></label>
                                        <textarea id="description_ar" class="form-control" name="description[ar]" rows="4" required minlength="20"></textarea>
                                    </div>
                                </div>

                                <!-- Description English -->
                                <div class="col-md-6 col-12">
                                    <div class="form-group">
                                        <label for="description_en">وصف الدورة (إنجليزي) <span class="text-danger">*</span></label>
                                        <textarea id="description_en" class="form-control" name="description[en]" rows="4" required minlength="20"></textarea>
                                    </div>
                                </div>

                                <!-- Active Status -->
                                <div class="col-12">
                                    <div class="form-group">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input" id="is_active" name="is_active" value="1" checked>
                                            <label class="custom-control-label" for="is_active">مفعل للعملاء</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Course Stages Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">مراحل الدورة</h4>
                    <button type="button" class="btn btn-primary" id="add-stage">إضافة مرحلة</button>
                </div>
                <div class="card-content">
                    <div class="card-body">
                        <div id="stages-container">
                            <!-- Stages will be added here dynamically -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12 d-flex justify-content-end">
            <button type="submit" class="btn btn-primary mr-1 mb-1">حفظ</button>
            <a href="<?php echo e(route('admin.courses.index')); ?>" class="btn btn-light-secondary mr-1 mb-1">إلغاء</a>
        </div>
    </div>
</section>
</form>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="<?php echo e(asset('admin/app-assets/vendors/js/forms/validation/jqBootstrapValidation.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/js/scripts/forms/validation/form-validation.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/vendors/js/extensions/sweetalert2.all.min.js')); ?>"></script>
<script src="<?php echo e(asset('admin/app-assets/js/scripts/extensions/sweet-alerts.js')); ?>"></script>


    <script>
        let stageIndex = 0;

        // Add stage functionality
        $('#add-stage').click(function() {
            addStage();
        });

        // Add initial stage
        $(document).ready(function() {
            addStage();
        });

        function addStage() {
            const stageHtml = `
                <div class="stage-item" data-index="${stageIndex}">
                    <div class="stage-header">
                        <h5>المرحلة ${stageIndex + 1}</h5>
                        <button type="button" class="remove-stage" onclick="removeStage(this)">حذف المرحلة</button>
                    </div>
                    <div class="row">
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (عربي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${stageIndex}][title][ar]" required minlength="5">
                            </div>
                        </div>
                        <div class="col-md-6 col-12">
                            <div class="form-group">
                                <label>عنوان المرحلة (إنجليزي) <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="stages[${stageIndex}][title][en]" required minlength="5">
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="form-group">
                                <label>فيديو المرحلة <span class="text-danger">*</span></label>
                                <input type="file" class="form-control" name="stages[${stageIndex}][video]" accept="video/*" required>
                                <small class="text-muted">الحد الأقصى لحجم الملف: 50 ميجابايت</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            $('#stages-container').append(stageHtml);
            stageIndex++;
            updateStageNumbers();
        }

        function removeStage(button) {
            if ($('.stage-item').length > 1) {
                $(button).closest('.stage-item').remove();
                updateStageNumbers();
            } else {
                Swal.fire('تنبيه!', 'يجب أن تحتوي الدورة على مرحلة واحدة على الأقل', 'warning');
            }
        }

        function updateStageNumbers() {
            $('.stage-item').each(function(index) {
                $(this).find('.stage-header h5').text('المرحلة ' + (index + 1));
            });
        }
    </script>

<?php echo $__env->make('admin.shared.addImage', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>



<?php echo $__env->make('admin.shared.submitAddForm', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('admin.layout.master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/courses/create.blade.php ENDPATH**/ ?>