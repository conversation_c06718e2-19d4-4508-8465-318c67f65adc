<div class="position-relative" style="overflow: auto">
    <table class="table" id="tab">
        <thead>
            <tr>
                <th>
                    <label class="container-checkbox">
                        <input type="checkbox" value="value1" name="name1" id="checkedAll">
                        <span class="checkmark"></span>
                    </label>
                </th>
                <th>التاريخ</th>
                <th>الصورة</th>
                <th>اسم الدورة</th>
                <th>المدرب</th>
                <th>المدة (ساعة)</th>
                <th>السعر</th>
                <th>عدد المراحل</th>
                <th>الحالة</th>
                <th>التحكم</th>
            </tr>
        </thead>
        <tbody>
            <?php $__empty_1 = true; $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                <tr class="delete_row">
                    <td class="text-center">
                        <label class="container-checkbox">
                            <input type="checkbox" class="checkSingle" id="<?php echo e($course->id); ?>">
                            <span class="checkmark"></span>
                        </label>
                    </td>
                    <td><?php echo e($course->created_at->format('d/m/Y')); ?></td>
                    <td>
                        <img src="<?php echo e($course->image); ?>" width="50px" height="50px" alt="course image" style="border-radius: 5px;">
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> <?php echo e($course->getTranslation('name', 'ar')); ?><br>
                            <strong>English:</strong> <?php echo e($course->getTranslation('name', 'en')); ?>

                        </div>
                    </td>
                    <td>
                        <div>
                            <strong>عربي:</strong> <?php echo e($course->getTranslation('instructor_name', 'ar')); ?><br>
                            <strong>English:</strong> <?php echo e($course->getTranslation('instructor_name', 'en')); ?>

                        </div>
                    </td>
                    <td><?php echo e($course->duration); ?></td>
                    <td><?php echo e(number_format($course->price, 2)); ?> ريال</td>
                    <td>
                        <span class="badge badge-info"><?php echo e($course->stages_count); ?> مرحلة</span>
                    </td>
                    <td>
                        <?php if($course->is_active): ?>
                            <span class="btn btn-sm round btn-outline-success toggle-status" data-id="<?php echo e($course->id); ?>">
                                مفعل <i class="la la-check font-medium-2"></i>
                            </span>
                        <?php else: ?>
                            <span class="btn btn-sm round btn-outline-danger toggle-status" data-id="<?php echo e($course->id); ?>">
                                غير مفعل <i class="la la-close font-medium-2"></i>
                            </span>
                        <?php endif; ?>
                    </td>
                    <td class="product-action">
                        <span class="text-primary"><a href="<?php echo e(route('admin.courses.show', ['id' => $course->id])); ?>" class="btn btn-warning btn-sm"><i class="feather icon-eye"></i> <?php echo e(__('admin.show')); ?></a></span>
                        <span class="action-edit text-primary"><a href="<?php echo e(route('admin.courses.edit', ['id' => $course->id])); ?>" class="btn btn-primary btn-sm"><i class="feather icon-edit"></i><?php echo e(__('admin.edit')); ?></a></span>
                        <span class="delete-row btn btn-danger btn-sm" data-url="<?php echo e(url('admin/courses/' . $course->id)); ?>"><i class="feather icon-trash"></i><?php echo e(__('admin.delete')); ?></span>
                    </td>
                </tr>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                <tr>
                    <td colspan="10" class="text-center">
                        <div class="alert alert-info">
                            <i class="la la-info-circle"></i> لا توجد دورات تدريبية حتى الآن
                        </div>
                    </td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>

    <?php if($courses->hasPages()): ?>
        <div class="d-flex justify-content-center">
            <?php echo e($courses->appends(request()->query())->links()); ?>

        </div>
    <?php endif; ?>
</div>
<?php /**PATH D:\Workstation\sorriso-backend\resources\views/admin/courses/table.blade.php ENDPATH**/ ?>